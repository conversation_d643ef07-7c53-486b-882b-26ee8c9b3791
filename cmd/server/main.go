package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/99designs/gqlgen/graphql/playground"
	_ "github.com/lib/pq"

	"xbit-cdn-service/internal/config"
	"xbit-cdn-service/internal/handler"
	"xbit-cdn-service/internal/repository"
	"xbit-cdn-service/internal/service"
	"xbit-cdn-service/pkg/auth"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database connection
	db, err := initDatabase(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Initialize services
	r2Service, err := service.NewR2Service(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize R2 service: %v", err)
	}

	// Initialize CDN service (optional)
	cdnService, err := service.NewCDNService(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize CDN service: %v", err)
	}

	// Initialize repositories
	fileRepo := repository.NewFileRepository(db)

	// Initialize services
	fileService := service.NewFileService(fileRepo, r2Service, cdnService, cfg)

	// Initialize JWT service
	jwtService := auth.NewJWTService(cfg.JWT.Secret, cfg.JWT.Expiry)

	// Initialize GraphQL handler
	graphqlHandler := handler.NewGraphQLHandler(fileService)

	// Create HTTP server
	srv := createServer(graphqlHandler, jwtService, cfg)

	// Start server
	log.Printf("Database connection established to %s:%s/%s", cfg.Database.Host, cfg.Database.Port, cfg.Database.Name)
	log.Printf("Server starting on port %s", cfg.Server.Port)

	// Print welcome message similar to xbit-agent
	printWelcomeMessage(cfg)

	if err := srv.ListenAndServe(); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

func initDatabase(cfg *config.Config) (*sql.DB, error) {
	// Build connection string using config
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		cfg.Database.Host, cfg.Database.Port, cfg.Database.User,
		cfg.Database.Password, cfg.Database.Name, cfg.Database.SSLMode)

	// Open database connection
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(cfg.Database.MaxConnections)
	db.SetMaxIdleConns(cfg.Database.MaxIdleConnections)
	db.SetConnMaxLifetime(cfg.Database.ConnectionTimeout)
	db.SetConnMaxIdleTime(cfg.Database.IdleTimeout)

	// Test connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Printf("Database connection established to %s:%s/%s",
		cfg.Database.Host, cfg.Database.Port, cfg.Database.Name)
	return db, nil
}

func createServer(graphqlHandler *handler.GraphQLHandler, jwtService *auth.JWTService, cfg *config.Config) *http.Server {
	mux := http.NewServeMux()

	// Initialize auth middleware and handlers
	authMiddleware := auth.NewAuthMiddleware(jwtService, false)
	authHandler := auth.NewAuthHandler(jwtService)
	graphqlAuthMiddleware := auth.NewGraphQLAuthMiddleware(jwtService)

	// Authentication endpoints
	mux.HandleFunc("/auth/login", authHandler.Login)
	mux.HandleFunc("/auth/refresh", authHandler.Refresh)
	mux.Handle("/auth/me", authMiddleware.RequireAuth(http.HandlerFunc(authHandler.Me)))

	// API endpoints with xbit-agent style routing
	// Health check endpoints
	mux.HandleFunc("/api/cdn-service/graphql/ping", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"status": "healthy", "service": "xbit-cdn-service", "timestamp": "%s"}`,
			fmt.Sprintf("%d", cfg.Server.Port))
	})

	mux.HandleFunc("/api/cdn-service/graphql/healthz", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"status": "healthy", "service": "xbit-cdn-service"}`)
	})

	// GraphQL endpoint with authentication context
	mux.HandleFunc("/api/cdn-service/graphql", func(w http.ResponseWriter, r *http.Request) {
		// Extract user context for GraphQL
		ctx := graphqlAuthMiddleware.ExtractUserFromRequest(r)
		r = r.WithContext(ctx)

		w.Header().Set("Content-Type", "application/json")

		if r.Method == "GET" {
			// Handle introspection or simple queries
			fmt.Fprintf(w, `{"data": {"health": "OK"}}`)
			return
		}

		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Parse GraphQL query from request body
		var requestBody struct {
			Query string `json:"query"`
		}

		if err := json.NewDecoder(r.Body).Decode(&requestBody); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}

		// Simple GraphQL query handling
		response := handleGraphQLQuery(r.Context(), requestBody.Query)

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	})

	// GraphQL playground
	mux.Handle("/api/cdn-service/graphql/playground", playground.Handler("GraphQL playground", "/api/cdn-service/graphql"))

	// Legacy health check endpoint (no auth required) - for backward compatibility
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"status": "healthy", "service": "xbit-cdn-service"}`)
	})

	// Legacy GraphQL endpoints for backward compatibility
	mux.HandleFunc("/graphql", func(w http.ResponseWriter, r *http.Request) {
		// Redirect to new API endpoint
		http.Redirect(w, r, "/api/cdn-service/graphql", http.StatusMovedPermanently)
	})

	mux.Handle("/playground", playground.Handler("GraphQL playground", "/api/cdn-service/graphql"))

	// File upload endpoint (requires authentication)
	mux.Handle("/api/cdn-service/upload", authMiddleware.RequirePermission(auth.PermissionWriteFiles)(
		http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.Method != "POST" {
				http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
				return
			}

			// TODO: Implement file upload handling
			w.Header().Set("Content-Type", "application/json")
			fmt.Fprintf(w, `{"message": "Upload endpoint - TODO: implement"}`)
		}),
	))

	// Legacy upload endpoint for backward compatibility
	mux.Handle("/upload", authMiddleware.RequirePermission(auth.PermissionWriteFiles)(
		http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Redirect to new API endpoint
			http.Redirect(w, r, "/api/cdn-service/upload", http.StatusMovedPermanently)
		}),
	))

	// Apply CORS middleware to all routes
	handler := auth.CORSMiddleware(mux)

	return &http.Server{
		Addr:    ":" + cfg.Server.Port,
		Handler: handler,
	}
}

// CORS middleware
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// JWT middleware (placeholder)
func jwtMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// TODO: Implement JWT token validation
		// For now, just pass through
		next.ServeHTTP(w, r)
	})
}

// handleGraphQLQuery handles simple GraphQL queries for testing authentication
func handleGraphQLQuery(ctx context.Context, query string) map[string]interface{} {
	// Check if user is authenticated
	claims, isAuthenticated := auth.GetClaimsFromContext(ctx)

	// Simple query parsing
	query = strings.TrimSpace(query)

	// Handle health query (no auth required)
	if strings.Contains(query, "health") {
		return map[string]interface{}{
			"data": map[string]interface{}{
				"health": "OK",
			},
		}
	}

	// Handle files query (requires auth)
	if strings.Contains(query, "files") {
		if !isAuthenticated {
			return map[string]interface{}{
				"errors": []map[string]interface{}{
					{
						"message": "Authentication required to access files",
						"extensions": map[string]interface{}{
							"code": "UNAUTHENTICATED",
						},
					},
				},
			}
		}

		return map[string]interface{}{
			"data": map[string]interface{}{
				"files": map[string]interface{}{
					"files":           []interface{}{},
					"totalCount":      0,
					"hasNextPage":     false,
					"hasPreviousPage": false,
				},
			},
			"extensions": map[string]interface{}{
				"user": map[string]interface{}{
					"id":       claims.UserID,
					"username": claims.Username,
					"roles":    claims.Roles,
				},
			},
		}
	}

	// Handle uploadFile mutation (requires auth)
	if strings.Contains(query, "uploadFile") {
		if !isAuthenticated {
			return map[string]interface{}{
				"errors": []map[string]interface{}{
					{
						"message": "Authentication required to upload files",
						"extensions": map[string]interface{}{
							"code": "UNAUTHENTICATED",
						},
					},
				},
			}
		}

		return map[string]interface{}{
			"data": map[string]interface{}{
				"uploadFile": map[string]interface{}{
					"success": false,
					"message": "Upload functionality not implemented yet",
				},
			},
		}
	}

	// Default response for unknown queries
	return map[string]interface{}{
		"errors": []map[string]interface{}{
			{
				"message": "Unknown query",
				"extensions": map[string]interface{}{
					"code": "GRAPHQL_VALIDATION_FAILED",
				},
			},
		},
	}
}

// printWelcomeMessage prints a welcome message similar to xbit-agent
func printWelcomeMessage(cfg *config.Config) {
	host := cfg.Server.Host
	if host == "0.0.0.0" || host == "" {
		host = "127.0.0.1"
	}

	fmt.Printf("\n        Welcome to XBIT CDN Service\n")
	fmt.Printf("        Current Version: v1.0.0\n")
	fmt.Printf("        Environment: %s\n", cfg.Server.Env)
	fmt.Printf("        Database: %s@%s:%s/%s\n", cfg.Database.User, cfg.Database.Host, cfg.Database.Port, cfg.Database.Name)
	fmt.Printf("        Server: %s:%s\n", host, cfg.Server.Port)
	fmt.Printf("        GraphQL Playground: http://%s:%s/api/cdn-service/graphql/playground\n", host, cfg.Server.Port)
	fmt.Printf("        GraphQL Endpoint: http://%s:%s/api/cdn-service/graphql\n", host, cfg.Server.Port)
	fmt.Printf("        Upload Endpoint: http://%s:%s/api/cdn-service/upload\n", host, cfg.Server.Port)
	fmt.Printf("        Health Check: http://%s:%s/api/cdn-service/graphql/healthz\n", host, cfg.Server.Port)
	fmt.Printf("        Legacy Endpoints: http://%s:%s/playground, http://%s:%s/graphql, http://%s:%s/health\n", host, cfg.Server.Port, host, cfg.Server.Port, host, cfg.Server.Port)
	fmt.Println()
}
